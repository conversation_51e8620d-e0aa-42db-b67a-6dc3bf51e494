import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from visdom import Visdom
from sklearn.manifold import TSNE
from torch.nn import functional as F
from torch.utils.checkpoint import checkpoint
import math
from torch.autograd import Variable
from itertools import cycle
from typing import Optional
import os
from typing import Tuple
import pandas as pd


# python -m visdom.server
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_XTrainDataset(Dataset):
    def __init__(self):
        # 输入：四倍下采样后数据
        train_normal_down4 = scio.loadmat('E:\\pythonProject\\Puchuang\\Swin_TRM\\normal_1500_15_ax_low_train_1024.mat')
        normal_down4 = train_normal_down4['normal_1500_15_ax_low_train_1024']

        self.normal_data1_down4 = normal_down4[0, 0:921600]   # 样本数：900

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 1024)    # [B, C, H, W]

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4
        self.len = self.x_data.shape[0]

    def __getitem__(self, item):
        x = self.x_data[item]
        return x

    def __len__(self):
        return self.len


class Looseslipper_YTrainDataset(Dataset):
    def __init__(self):
        # 输出：原高频振动信号
        train_normal = scio.loadmat('E:\\pythonProject\\Puchuang\\Swin_TRM\\normal_1500_15_ax_high_train_4096.mat')
        normal = train_normal['normal_1500_15_ax_high_train_4096']

        self.normal_data1 = normal[0, 0:3686400]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data
        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        y = self.y_data[item]
        return y

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 1
# 训练集
train_x_dataset = Looseslipper_XTrainDataset()
train_x_loader = DataLoader(dataset=train_x_dataset,
                            batch_size=batch_size,
                            shuffle=False)

train_y_dataset = Looseslipper_YTrainDataset()
train_y_loader = DataLoader(dataset=train_y_dataset,
                            batch_size=batch_size,
                            shuffle=False)


SCALE_NUM = 4

# 融合部分的 Inter-attention
# Inter-attention参数设置
d_model = 64    # Embedding的维度, 等于通道数
n_heads = 8     # Multi-Head Attention设置为8，d_model / n_head = d_k = d_v
d_k = d_v = 8   # 多头注意力机制中，K(=Q),V的维度


# Mask掉停用词，对padding进行掩码的Mask
def get_attn_pad_mask(seq_q, seq_k):      # seq_q: [batch_size, seq_len]，seq_k: [batch_size, seq_len]
    batch_size, len_q = seq_q.size()[0], seq_q.size()[1]
    batch_size, len_k = seq_k.size()[0], seq_k.size()[1]
    return torch.ones((batch_size, len_q, len_k), device=seq_q.device, dtype=torch.bool)      # 使padding掩码不起作用


# 缩放点积注意力机制
class ScaledDotProductAttention(nn.Module):
    def __init__(self, use_Dropkey=False, mask_ratio=0.1):
        super(ScaledDotProductAttention, self).__init__()
        self.use_Dropkey = use_Dropkey
        self.mask_ratio = mask_ratio

    def forward(self, Q, K, V, attn_mask):  # Q: [batch_size, n_heads, len_q, d_k]
                                            # K: [batch_size, n_heads, len_k, d_k]
                                            # V: [batch_size, n_heads, len_v(=len_k), d_v]
        scores = torch.matmul(Q, K.transpose(-1, -2)) / np.sqrt(d_k)    # scores: [batch_size, n_heads, len_q, len_k]，计算Q*(K)T/sqrt(dk)

        # Apply DropKey
        if self.training and self.use_Dropkey:
            m_r = torch.ones_like(scores) * self.mask_ratio
            scores = scores + torch.bernoulli(m_r) * -1e12

        scores = torch.where(attn_mask, torch.tensor(-1e9, device=attn_mask.device), scores)
        attn = nn.Softmax(dim=-1)(scores)   # 计算归一化权重的注意力分数
        context = torch.matmul(attn, V)     # context: [batch_size, n_heads, len_q, d_v]
        return context, attn


# 多头注意力机制+残差+归一化
class MultiHeadAttention(nn.Module):
    def __init__(self):
        super(MultiHeadAttention, self).__init__()
        self.W_Q = nn.Linear(d_model, d_k * n_heads, bias=False)  # W_Q: [d_model, d_model]
        self.W_K = nn.Linear(d_model, d_k * n_heads, bias=False)  # W_K: [d_model, d_model]
        self.W_V = nn.Linear(d_model, d_v * n_heads, bias=False)  # W_V: [d_model, d_model]
        self.fc = nn.Linear(n_heads * d_v, d_model, bias=False)   # 使输入与输出的维度相同

    def forward(self, input_Q, input_K, input_V, attn_mask):  # input_Q: [batch_size, len_q, d_model]，输入W_Q,W_K,W_V的为经过Embedding和位置编码后的向量
                                                              # input_K: [batch_size, len_k, d_model]，input_Q=input_K=input_V=input
                                                              # input_V: [batch_size, len_v(=len_k), d_model]
        residual, batch_size = input_Q, input_Q.size(0)       # residual=input, 为经过embedding和位置编码的输入向量
        # 先做线性变换，d_model -> n_head * d_k，后拆分多头矩阵
        Q = self.W_Q(input_Q).view(batch_size, -1, n_heads, d_k).transpose(1, 2)   # Q: [batch_size, n_heads, len_q, d_k]，查询矩阵，Q=input * W_Q
        K = self.W_K(input_K).view(batch_size, -1, n_heads, d_k).transpose(1, 2)   # K: [batch_size, n_heads, len_k, d_k]，键矩阵，K=input * W_K
        V = self.W_V(input_V).view(batch_size, -1, n_heads, d_v).transpose(1, 2)   # V: [batch_size, n_heads, len_v(=len_k), d_v]，值矩阵，V=input * W_V
        attn_mask = attn_mask.unsqueeze(1).repeat(1, n_heads, 1, 1)
        context, attn = ScaledDotProductAttention()(Q, K, V, attn_mask)  # context: [batch_size, n_heads, len_q, d_v]
                                                                         # attn: [batch_size, n_heads, len_q, len_k]
        context = context.transpose(1, 2).reshape(batch_size, -1, n_heads * d_v)   # context: [batch_size, len_q, n_heads * d_v]
        output = self.fc(context)    # [batch_size, len_q, d_model]
        return nn.LayerNorm(d_model).cuda()(output + residual), attn


def pixelshuffle(x: torch.Tensor, factor_hw: Tuple[int, int]):
    pH = factor_hw[0]     # 高度方向上采样因子，时序信号 pH=1
    pW = factor_hw[1]     # 宽度方向上采样因子

    B, iC, iH, iW = x.shape                          # 输入张量形状
    oC, oH, oW = iC // (pH * pW), iH * pH, iW * pW   # 输出张量形状
    x = x.reshape(B, oC, pH, pW, iH, iW)
    x = x.permute(0, 1, 4, 2, 5, 3)      # [B, oC, iH, pH, iW, pW]
    x = x.reshape(B, oC, oH, oW)         # [B, oC, iH * pH, iW * pW]
    return x                             # [B, iC//pW, 1, iW * pW]


def drop_path_f(x, drop_prob: float = 0., training: bool = False):
    if drop_prob == 0. or not training:
        return x
    keep_prob = 1 - drop_prob
    shape = (x.shape[0],) + (1,) * (x.ndim - 1)
    random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
    random_tensor.floor_()
    output = x.div(keep_prob) * random_tensor
    return output


class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        return drop_path_f(x, self.drop_prob, self.training)


# 窗口的划分和feature map的还原
# 将feature map划分成一个个window
def window_partition(x, window_size: int):
    # 将feature map按照window_size划分成一个个没有重叠的window
    B, H, W, C = x.shape     # x: [B, H, W, C], [1, 1, 1024, 32]
    x = x.view(B, H // 1, 1, W // window_size, window_size, C)    # [B, H//M, M, W//M, M, C], [1, 1, 1, 8, 128, 32]
    # permute: [B, H//M, M, W//M, M, C] -> [B, H//M, W//M, M, M, C], [1, 1, 8, 1, 32, 32]
    # view: [B, H//M, W//M, M, M, C] -> [B*num_windows, M, M, C], [8, 1, 32, 32]
    windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, 1, window_size, C)    # [8, 1, 128, 32]
    return windows     # [B*num_windows, Mh, Mw, C], [8, 1, 128, 32]


# 将window还原成feature map
def window_reverse(windows, window_size: int, H: int, W: int):        # H, W是划分window前feature map的H和W
    B = int(windows.shape[0] / (H * W / 1 / window_size))   # windows: [B*(H//M)*(W//M), M, M, C], [8, 1, 32, 32]
    # view: [B*num_windows, M, M, C] -> [B, H//M, W//M, M, M, C]
    x = windows.view(B, H // 1, W // window_size, 1, window_size, -1)    # [1, 1, 8, 1, 32, 96]
    # permute: [B, H//M, W//M, M, M, C] -> [B, H//M, M, W//M, M, C], [1, 1, 1, 8, 32, 96]
    # view: [B, H//M, M, W//M, M, C] -> [B, H, W, C], [1, 1, 256, 96]
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return x          # [B, H, W, C], 和window_partition输入数据形式相同, [1, 1, 1024, 32]


# Patch Partiton + Linear Embedding
class PatchEmbed(nn.Module):
    def __init__(self, patch_size=4, in_channel=1, emb_dim=64, norm_layer=None):
        super(PatchEmbed, self).__init__()
        # patch_size = (1, patch_size)     # [1, 4]
        # self.patch_size = patch_size
        self.in_channel = in_channel
        self.emb_dim = emb_dim
        # self.proj = nn.Conv2d(1, emb_dim, kernel_size=patch_size, stride=patch_size)    # [B,96,224/4,224/4], [B,64,1,1024/4]
        self.norm = norm_layer(emb_dim) if norm_layer else nn.Identity()

    def forward(self, x):
        _, _, H, W = x.shape    # 获取图像高度H和宽度W, H=1, W=1024, [1, 32, 1, 1024]

        # # 若输入图片的H，W不是patch_size的整数倍，需要进行padding
        # pad_input = W % self.patch_size[1] != 0     # 检查W能否被patch_size整除
        # if pad_input:
        #     x = F.pad(x, (0, self.patch_size[1] - W % self.patch_size[1],    # 在宽度方向的右侧padding
        #                   0, 0,                                              # 在高度方向的底部padding
        #                   0, 0))                                             # 深度方向不padding
        #
        # # 下采样patch_size倍
        # x = self.proj(x)      # [1, 32, 1, 256]
        # _, _, H, W = x.shape    # 记录下采样之后的 H 和 W, H=1, W=256
        # flatten: [B, C, H, W] -> [B, C, HW], [B,96,224/4,224/4] -> [B,96,56*56]
        # transpose: [B, C, HW] -> [B, HW, C], [B,56*56,96]
        x = x.flatten(2).transpose(1, 2)     # [1, 1024, 32]
        x = self.norm(x)
        return x, H, W       # x: [B, HW, C], 下采样之后的高度H和宽度W, [1, 1024, 32]


class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super(Mlp, self).__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features

        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.drop1 = nn.Dropout(drop)
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop2 = nn.Dropout(drop)

    def forward(self, x):   # [1, 256, 96]
        x = self.fc1(x)     # [1, 256, 96*4]
        x = self.act(x)
        x = self.drop1(x)
        x = self.fc2(x)     # [1, 256, 96]
        x = self.drop2(x)
        return x


# Window Attention, 实现W-MSA的功能以及SW-MSA的部分功能
class WindowAttention(nn.Module):
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0., use_Dropkey=False, mask_ratio=0.1):
        super(WindowAttention, self).__init__()
        self.dim = dim
        self.window_size = window_size    # [Mh, Mw], [1, 128]
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5     # 1/sqrt(d)

        # define a parameter table of relative position bias, 相对位置偏置
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))    # [2*Mh-1 * 2*Mw-1, nH], 针对不同head所采用的相对位置偏置表不同

        # get pair-wise relative position index for each token inside the window, 生成相对位置索引
        coords_h = torch.arange(self.window_size[0])   # 1
        coords_w = torch.arange(self.window_size[1])   # 128
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing="ij"))    # [2, Mh, Mw], [2, 1, 128]
        coords_flatten = torch.flatten(coords, 1)    # [2, Mh*Mw], 绝对位置索引, [2, 1*32]
        # [2, Mh*Mw, 1] - [2, 1, Mh*Mw]
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]    # [2, Mh*Mw, Mh*Mw], 相减得到相对位置索引矩阵
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()              # [Mh*Mw, Mh*Mw, 2], [32, 32, 2]
        # 相对位置索引矩阵：二元索引 -> 一元索引
        relative_coords[:, :, 0] += self.window_size[0] - 1         # 行标加上（M-1）
        relative_coords[:, :, 1] += self.window_size[1] - 1         # 列标加上（M-1）
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1     # 行标乘以（2M-1）
        relative_position_index = relative_coords.sum(-1)           # [Mh*Mw, Mh*Mw]，行标与列标相加，得到一元索引->相对位置索引, [1*32, 1*32]
        self.register_buffer("relative_position_index", relative_position_index)     # 相对位置索引index是不变的，通过训练改变的是相对位置偏置表table

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)     # 初始化 relative_position_bias_table
        self.softmax = nn.Softmax(dim=-1)

        self.use_Dropkey = use_Dropkey
        self.mask_ratio = mask_ratio

    def forward(self, x, mask: Optional[torch.Tensor] = None):
        # [batch_size*num_windows, Mh*Mw, total_embed_dim]
        B_, N, C = x.shape    # [1*8, 1*32, 96]
        # qkv(): -> [batch_size*num_windows, Mh*Mw, 3 * total_embed_dim]
        # reshape: -> [batch_size*num_windows, Mh*Mw, 3, num_heads, embed_dim_per_head]
        # permute: -> [3, batch_size*num_windows, num_heads, Mh*Mw, embed_dim_per_head], [3, 8, 3, 32, 32]
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        # [batch_size*num_windows, num_heads, Mh*Mw, embed_dim_per_head]
        q, k, v = qkv.unbind(0)    # [B*8, head, 1*32, C_h], [8, 3, 32, 32]

        # transpose: -> [batch_size*num_windows, num_heads, embed_dim_per_head, Mh*Mw]
        # @: multiply -> [batch_size*num_windows, num_heads, Mh*Mw, Mh*Mw]
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))    # attn: [batch_size*num_windows, num_heads, Mh*Mw, Mh*Mw], [8, 3, 32, 32]

        # 通过相对位置索引在相对位置偏置表中获得相对位置偏置
        # relative_position_bias_table.view: [Mh*Mw*Mh*Mw, nH] -> [Mh*Mw, Mh*Mw, nH]
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)       # [32, 32, 3]
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()   # [nH, Mh*Mw, Mh*Mw], [3, 32, 32]
        attn = attn + relative_position_bias.unsqueeze(0)   # QK'/sqrt(d) + B, [8, 3, 32, 32]

        # Apply DropKey
        if self.training and self.use_Dropkey:
            m_r = torch.ones_like(attn) * self.mask_ratio
            attn = attn + torch.bernoulli(m_r) * -1e12

        if mask is not None:
            # mask: [nW, Mh*Mw, Mh*Mw]
            nW = mask.shape[0]    # num_windows
            # attn.view: [batch_size, num_windows, num_heads, Mh*Mw, Mh*Mw]
            # mask.unsqueeze: [1, nW, 1, Mh*Mw, Mh*Mw]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)   # mask对相同区域加0，不同区域加（-100）
            attn = attn.view(-1, self.num_heads, N, N)    # [batch_size*num_windows, num_heads, Mh*Mw, Mh*Mw]
            attn = self.softmax(attn)
        else:
            attn = self.softmax(attn)     # [8, 3, 32, 32]

        attn = self.attn_drop(attn)

        # @: multiply -> [batch_size*num_windows, num_heads, Mh*Mw, embed_dim_per_head]
        # transpose: -> [batch_size*num_windows, Mh*Mw, num_heads, embed_dim_per_head]
        # reshape: -> [batch_size*num_windows, Mh*Mw, total_embed_dim]
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)    # [8, 32, 96]
        x = self.proj(x)
        x = self.proj_drop(x)
        return x     # [8, 32, 96]


# Swin Transformer Block
class SwinTransformerBlock(nn.Module):
    def __init__(self, dim, num_heads, window_size=(32, 64, 128), shift_size=0,
                 mlp_ratio=4., qkv_bias=True, drop=0., attn_drop=0., drop_path=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super(SwinTransformerBlock, self).__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio
        assert 0 <= self.shift_size < self.window_size, "shift_size must in 0 ~ window_size"    # shift size=0 W-MSA, shift size>0 SW-MSA

        self.norm1 = norm_layer(dim)
        # W-MSA / SW-MSA
        self.attn = WindowAttention(
            dim, window_size=(1, self.window_size), num_heads=num_heads, qkv_bias=qkv_bias,
            attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_layer = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_layer, act_layer=act_layer, drop=drop)

    def forward(self, x, attn_mask):
        H, W = self.H, self.W     # H=1, W=1024
        B, L, C = x.shape         # x: [1, 1024, 32]
        assert L == H * W, "input feature is wrong size"

        shortcut = x
        x = self.norm1(x)
        x = x.view(B, H, W, C)    # x: [B, H, W, C], [1, 1, 1024, 32]

        # pad feature maps to multiples of window size
        # 把feature map给pad到window size的整数倍
        pad_l = pad_t = pad_b = 0    # feature map的左侧和上侧padding为0，只在右侧和下侧padding
        pad_r = (self.window_size - W % self.window_size) % self.window_size   # 计算在W方向右侧padding的列数
        # pad_b = (self.window_size - H % self.window_size) % self.window_size   # 计算在H方向下侧padding的行数
        x = F.pad(x, (0, 0, pad_l, pad_r, pad_t, pad_b))      # padding宽度和高度方向
        _, Hp, Wp, _ = x.shape       # 获取padding之后的H和W

        # cyclic shift循环移位, 若shift_size大于零为SW-MSA, 若等于0为W-MSA
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(0, -self.shift_size), dims=(1, 2))   # 将上侧shift_size行移到下面，将左侧shift_size列移到右侧
        else:
            shifted_x = x
            attn_mask = None

        # partition windows
        x_windows = window_partition(shifted_x, self.window_size)       # [nW*B, Mh, Mw, C], [8, 1, 128, 32]
        x_windows = x_windows.view(-1, 1 * self.window_size, C)         # [nW*B, Mh*Mw, C], [8, 128, 32]

        # W-MSA/SW-MSA
        attn_windows = self.attn(x_windows, mask=attn_mask)             # [nW*B, M*M, C], [8, 128, 32]

        # merge windows
        attn_windows = attn_windows.view(-1, 1, self.window_size, C)    # [nW*B, Mh, Mw, C], [8, 1, 128, 32]
        shifted_x = window_reverse(attn_windows, self.window_size, Hp, Wp)      # [B, H', W' C], [1, 1, 1024, 32]

        # reverse cyclic shift
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(0, self.shift_size), dims=(1, 2))     # SW-MSA
        else:
            x = shifted_x      # W-MSA

        # 若进行了padding, 需将padding部分移除
        if pad_r > 0 or pad_b > 0:
            # 将pad的数据移除
            x = x[:, :H, :W, :].contiguous()

        x = x.view(B, H * W, C)      # x: [B, L, C], [1, 1024, 32]

        # FFN
        x = shortcut + self.drop_path(x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


# A basic Swin Transformer layer for one stage: 前一个stage的block+后一个stage的Patch Merging
class BasicLayer(nn.Module):
    def __init__(self, dim, depth, num_heads, window_size,
                 mlp_ratio=4., qkv_bias=True, drop=0., attn_drop=0.,
                 drop_path=[], norm_layer=nn.LayerNorm, downsample=None, use_checkpoint=False):
        super().__init__()
        self.dim = dim         # 输入通道数, token维度
        self.depth = depth     # block的数量
        self.window_size = window_size         # 局部窗口大小
        self.use_checkpoint = use_checkpoint
        self.shift_size = window_size // 2     # 窗口向右、向下移动窗口大小一半(M/2, M/2)，向下取整

        # 构建所有Swin Transformer Block
        self.blocks = nn.ModuleList([
            SwinTransformerBlock(
                dim=dim,
                num_heads=num_heads,
                window_size=window_size,
                shift_size=0 if (i % 2 == 0) else self.shift_size,    # 判断执行的是W-MSA还是SW-MSA：i=0, W-MSA; i=1, SW-MSA
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                drop=drop,
                attn_drop=attn_drop,
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                norm_layer=norm_layer)
            for i in range(depth)])       # 每个stage循环的block次数：depth=2, W-MSA -> SW-MSA
                                          # depth=6, (W-MSA -> SW-MSA) *3
        # 构建patch merging layer
        if downsample is not None:
            self.downsample = downsample(dim=dim, norm_layer=norm_layer)     # patch merging
        else:
            self.downsample = None

    def create_mask(self, x, H, W):
        # calculate attention mask for SW-MSA
        # Padding, 保证Hp和Wp是window_size的整数倍
        Hp = int(np.ceil(H / 1)) * 1    # （H / window_size）向上取整 * window_size
        Wp = int(np.ceil(W / self.window_size)) * self.window_size    # 得到H padding之后的值Hp，和W padding之后的值Wp
        # 拥有和feature map一样的通道排列顺序，方便后续window_partition
        img_mask = torch.zeros((1, Hp, Wp, 1), device=x.device)       # [1, Hp, Wp, 1], B=C=1
        # 切片操作，slice(start, stop, step)，step默认为1
        # h_slices = (slice(0, -self.window_size),                      # h方向分为三个切片
        #             slice(-self.window_size, -self.shift_size),
        #             slice(-self.shift_size, None))
        w_slices = (slice(0, -self.window_size),                      # w方向分为三个切片
                    slice(-self.window_size, -self.shift_size),
                    slice(-self.shift_size, None))
        cnt = 0
        for w in w_slices:                 # 遍历宽度方向三个切片
            img_mask[:, :, w, :] = cnt     # 构造分区矩阵，相同数字对应连续区域
            cnt += 1

        mask_windows = window_partition(img_mask, self.window_size)     # [num_windows, Mh, Mw, 1], img_mask的B=1, C=1
        mask_windows = mask_windows.view(-1, 1 * self.window_size)      # [num_windows, Mh * Mw]
        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)             # [num_windows, 1, Mh*Mw] - [num_windows, Mh*Mw, 1], 广播机制
        # [num_windows, Mh*Mw, Mh*Mw] - [num_windows, Mh*Mw, Mh*Mw]，第一个矩阵行向量复制M*M次，第二个矩阵最后一个维度复制M*M次，相减后相同区域为0，不同区域不为0
        attn_mask = torch.where(attn_mask != 0, torch.tensor(float(-100.0), device=attn_mask.device),
                                torch.tensor(float(0.0), device=attn_mask.device))    # 不同区域填充-100，相同区域填充0
        return attn_mask

    def forward(self, x, H, W):
        # x: [1, 1024, 32]
        attn_mask = self.create_mask(x, H, W)   # SW-MSA计算中的掩码，每个stage中的mask均相同（transformer不改变图像尺寸）
        # [nW, Mh*Mw, Mh*Mw]
        for blk in self.blocks:     # 遍历Swin Transformer Block
            blk.H, blk.W = H, W     # 给blk添加H高度和W宽度属性
            if self.use_checkpoint:
                x = checkpoint.checkpoint(blk, x, attn_mask)
            else:
                x = blk(x, attn_mask)           # 一般执行这一步，将x和attn_mask送入swin transformer block，得到输出
        # if self.downsample is not None:
        #     x = self.downsample(x, H, W)        # patch merging layer
        #     H, W = 1, (W + 1) // 2              # 计算patch merging后的H和W，H和W减半
                                                # 若H, W为奇数，padding后下采样；若为偶数，加一除以二向下取整还是原来的一半
        return x, H, W                          # x：经过swin transformer block+patch merging后的输出；H、W：patch merging后的图像尺寸
                                                # x: [1, 1024, 32]

class SwinTransformer(nn.Module):
    def __init__(self, patch_size=4, in_channel=1, num_classes=4096,
                 embed_dim=64, depths=(2, 2, 2), num_heads=(4, 4, 8),
                 window_size=(32, 64, 128), mlp_ratio=4., qkv_bias=True,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, patch_norm=True,
                 use_checkpoint=False, **kwargs):
        super(SwinTransformer, self).__init__()

        # 浅层特征提取
        self.conv_shallow = torch.nn.Conv2d(1, 64, kernel_size=(1, 5), padding=(0, 2))     # 浅层特征提取
        self.bn1 = torch.nn.BatchNorm2d(64)

        # 高频特征提取
        self.conv_deep = torch.nn.Conv2d(32, 32, kernel_size=(1, 3), padding=(0, 1))       # 高频特征提取
        self.conv_deep_2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 1))

        # 深度可分离卷积：深度卷积+逐点卷积
        # 深度卷积
        self.depthwise = torch.nn.Conv2d(in_channels=64,
                                         out_channels=64,
                                         kernel_size=(1, 3),
                                         stride=1,
                                         padding=(0, 1),
                                         groups=64)
        # 逐点卷积
        self.pointwise = torch.nn.Conv2d(in_channels=64,
                                         out_channels=64,
                                         kernel_size=(1, 1))

        self.num_classes = num_classes
        self.num_layers = len(depths)  # num_layers=4
        self.embed_dim = embed_dim     # 向量维度
        self.patch_norm = patch_norm
        # stage4输出特征矩阵的channel
        # self.num_features = int(embed_dim * 2 ** (self.num_layers - 1))   # C*2^(num_layers-1)，最后一层的维度为8C
        self.num_features = 32
        self.mlp_ratio = mlp_ratio    # MPL的隐藏层将“维度数*4”

        # 将图片划分为不重叠的patches，实现 Patch Partiton + Linear Embedding
        self.patch_embed = PatchEmbed(
            patch_size=patch_size, in_channel=in_channel, emb_dim=embed_dim,
            norm_layer=norm_layer if self.patch_norm else None)
        self.pos_drop = nn.Dropout(p=drop_rate)

        # stochastic depth 随机深度，在前向传播过程中随机丢弃一部分网络层输出
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]   # 每一层Swin Tansformer Block生成一个从0逐渐增加到drop_path_rate的丢弃概率值

        # build layers
        self.layers = nn.ModuleList()      # 储存了所有stage, 从stage1的swin transformer block到末尾
        for i_layer in range(self.num_layers):      # i_layer: 0～3
            # 这里构建的stage和论文中有些差异
            # stage = 上个stage的swin transformer block + 下个stage的patch merging
            layers = BasicLayer(dim=64,   # token的维度：C * 2^(i_layer)
                                depth=depths[i_layer],            # i_layer层Block的堆叠次数
                                num_heads=num_heads[i_layer],     # i_layer层注意力头数
                                window_size=window_size[i_layer],          # 窗口大小
                                mlp_ratio=self.mlp_ratio,         # MPL的隐藏层维度数
                                qkv_bias=qkv_bias,                # qkv偏置
                                drop=drop_rate,
                                attn_drop=attn_drop_rate,
                                drop_path=dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],         # i_layer层的丢弃概率
                                norm_layer=norm_layer,
                                # downsample=PatchMerging if (i_layer < self.num_layers - 1) else None,  # 前三个stage有PatchMerging，最后一个没有
                                use_checkpoint=use_checkpoint)
            self.layers.append(layers)

        # 分类任务输出
        self.conv_cat = torch.nn.Conv2d(64, SCALE_NUM, kernel_size=(1, 1))

        self.conv_cat_parallel = torch.nn.Conv2d(64, 64, kernel_size=(1, 1))
        self.bn2 = torch.nn.BatchNorm2d(4)
        self.bn3 = torch.nn.BatchNorm2d(1)
        self.bn4 = torch.nn.BatchNorm2d(32)
        self.bn5 = torch.nn.BatchNorm2d(64)
        self.norm = norm_layer(64)
        self.mp = torch.nn.MaxPool2d(kernel_size=(1, 2), stride=1, padding=(0, 1))

        self.inter_attn = MultiHeadAttention()


        self.apply(self._init_wights)     # 权重初始化

    # 权重初始化
    def _init_wights(self, m):
        if isinstance(m, nn.Linear):          # nn.linear模块初始化
            nn.init.trunc_normal_(m.weight, std=.02)    # 初始化权重
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)  # 初始化偏置
        elif isinstance(m, nn.LayerNorm):     # nn.LayerNorm模块初始化
            nn.init.constant_(m.bias, 0)      # 初始化偏置为 0
            nn.init.constant_(m.weight, 1.0)  # 初始化权重为 1.0

    def forward(self, x):
        x_shallow = torch.relu(self.bn1(self.conv_shallow(x)))          # 浅层特征提取, [1, 32, 1, 1024]

        # 通过并联卷积分支进行高频特征提取
        high_part1, high_part2 = torch.tensor_split(x_shallow, [x_shallow.size(1) // 2], dim=1)      # 沿通道方向分成两个[1, 16, 1, 1024]，输入高频部分

        x_high = torch.relu(self.bn4(self.conv_deep(high_part1)))    # [1, 16, 1, 1024]，局部特征提取分支
        x_high = x_high.flatten(2).transpose(1, 2)                   # [1, 1024, 16]

        x_high_2 = self.mp(high_part2)                                   # [1, 16, 1, 1024]，高频增强分支
        x_high_2 = x_high_2[:, :, :, :1024]     # 裁剪至匹配的尺寸
        x_high_2 = torch.relu(self.bn4(self.conv_deep_2(x_high_2)))      # [1, 16, 1, 1024]
        x_high_2 = x_high_2.flatten(2).transpose(1, 2)                   # [1, 1024, 16]
        x_high_cat = [x_high, x_high_2]
        x_high_cat = torch.cat(x_high_cat, dim=-1)                       # [1, 1024, 32]

        # 通过swin-transformer进行低频特征提取, x: [B, L, C], [1, 1024, 32]
        x_low, H, W = self.patch_embed(x_shallow)     # x: [1, 1024, 32]
        x_low = self.pos_drop(x_low)              # 随机丢弃一部分输入
        shortcut = x_low

        for layer in self.layers:
            x_low, H, W = layer(x_low, H, W)      # x: [1, 1024, 32]
        x_low = x_low + shortcut

        inter_attn_outputs_mul = torch.add(x_high_cat, x_low)     # 高频与低频直接相加，[1, 1024, 128]
        inter_attn_outputs_mul = inter_attn_outputs_mul.permute(0, 2, 1)   # [1, 64, 1024]
        inter_attn_outputs_mul = inter_attn_outputs_mul.unsqueeze(2)       # [1, 64, 1, 1024]

        inter_attn_outputs_mul = self.conv_cat(inter_attn_outputs_mul)             # [1, 4, 1, 1024]

        inter_attn_outputs_mul = self.bn3(pixelshuffle(inter_attn_outputs_mul, (1, SCALE_NUM)))      # [1, 1, 1, 4096]

        return inter_attn_outputs_mul


model = SwinTransformer(in_chans=1,
                        patch_size=4,
                        window_size=(64, 128, 256),
                        embed_dim=64,
                        depths=(2, 2, 2),
                        num_heads=(4, 4, 8),
                        num_classes=4096,)
model.to(device)   # 将模型放到GPU上

# 损失函数和优化器
criterion = torch.nn.MSELoss()  # 均方差损失
# optimizer = optim.SGD(model.parameters(), lr=0.002, momentum=0.8)
optimizer = optim.Adam(model.parameters(), lr=0.002)
epoches = 2   # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([0.0],  # Y的第一个点坐标
                [0.0],  # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss', legend=['loss'])  # 图像的图例
                )

for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0
    batch_train = 0
    # 训练
    for (low_train, high_train) in zip(train_x_loader, train_y_loader):
        batch_train += 1
        # prepare data
        low_train = low_train.to(device)
        high_train = high_train.to(device)
        # forward
        outputs = model(low_train)
        # outputs: [batch_size * tgt_len, tgt_vocab_size]
        high_train = high_train.reshape(-1)      # 转换成一维张量
        outputs = outputs.reshape(-1)
        loss = criterion(outputs, high_train)
        print('Epoch:', '%04d' % (epoch + 1), 'loss =', '{:.6f}'.format(loss))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()
        # training curve
        train_loss += loss.item()
        global_iter_num_train = epoch * len(train_x_loader) + batch_train + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([loss.item()], [global_iter_num_train], win='train', update='append')  # 损失函数曲线

        print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))


# 保存
save_dir = r"E:\pythonProject\Puchuang\Swin_TRM\Swin_TRM.pth"  # 保存路径
torch.save(model.state_dict(), save_dir)


