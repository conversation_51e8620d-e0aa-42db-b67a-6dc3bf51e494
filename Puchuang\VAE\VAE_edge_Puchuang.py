import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_train_2048.mat')
        normal = train_normal['detection_normal_1500_15_train_2048']

        self.normal_data = normal[0, 0:1843200]   # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_val_2048.mat')
        normal = val_normal['detection_normal_1500_15_val_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('..\\dataset\\detection_loose_1500_15_val_2048.mat')
        loose8067 = val_loose8067['detection_loose_1500_15_val_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_test_2048.mat')
        normal = test_normal['detection_normal_1500_15_test_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('..\\dataset\\detection_loose_1500_15_test_2048.mat')
        loose8067 = test_loose8067['detection_loose_1500_15_test_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 64

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, latent_dim * 2),   # 同时输出：隐空间的均值向量mu、对数方差向量log_var
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, input_dim),
            nn.Sigmoid(),
        )

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        epsilon = torch.randn_like(mu)                 # 生成与mu形状相同的张量，其元素是从标准正态分布N(0,1)中随机抽取的
        return mu + epsilon * torch.exp(logvar / 2)    # 生成隐变量z，该变量服从以mu为均值，exp(logvar/2)为标准差的正态分布

    def forward(self, x):
        org_size = x.size()     # 获取输入x的原始尺寸
        batch = org_size[0]     # 获取批次大小
        x = x.view(batch, -1)   # 将x展平为二维向量，便于处理

        h = self.encoder(x)                  # 编码器处理输入x
        mu, logvar = h.chunk(2, dim=1)       # 将编码器输出分为两部分：均值mu和对数方差logvar

        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decoder(z).view(org_size)   # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar           # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='mean') * x.size(1)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss


# 计算新颖性评分
def calculate_novelty_score(recon_x, x, mu, logvar, recon_weight=0.5, kl_weight=0.5):
    recon_loss = F.mse_loss(recon_x.view_as(x), x, reduction='none').mean(dim=tuple(range(1, x.dim())))
    kl_divergence = -0.5 * torch.mean(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
    novelty_score = recon_weight * recon_loss + kl_weight * kl_divergence
    return novelty_score.cpu().numpy()


def find_best_threshold_roc(scores, true_labels):
    fpr, tpr, thresholds = roc_curve(true_labels, scores)
    # 计算每个阈值下的F1得分
    best_f1 = -1
    best_threshold = 0
    for i in range(len(thresholds)):
        predictions = (scores >= thresholds[i]).astype(int)
        f1 = f1_score(true_labels, predictions)
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = thresholds[i]
    return best_threshold, best_f1


# 绘制ROC曲线并计算AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    # 将ROC曲线的数据保存为CSV文件
    roc_data = pd.DataFrame({'False Positive Rate': fpr, 'True Positive Rate': tpr, 'Thresholds': thresholds})
    roc_data.to_csv('roc_data.csv', index=False)

    return roc_auc


# 初始化模型、优化器
vae = VAE(input_dim, inter_dim, latent_dim).to(device)
optimizer = optim.Adam(vae.parameters(), lr=0.002, weight_decay=1e-4)    # 设置L2正则化参数
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, verbose=True)


# 训练 & 验证
epochs = 6
# 初始化绘图数据
valid_losses = []
train_losses = []

# 创建绘图窗口
plt.figure(figsize=(10, 5))
plt.ion()   # 开启交互模式

best_loss = 1e9
best_epoch = 0

for epoch in range(epochs):
    print(f"Epoch {epoch}")
    # 将模型设置为训练模式
    vae.train()

    train_loss = 0.0
    train_num = len(train_loader.dataset)   # 获取训练集样本总数

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        batch = inputs.size(0)  # 获取当前样本数

        # forward
        recon_x, mu, logvar = vae(inputs)

        # 计算损失
        loss = vae_loss(recon_x, inputs, mu, logvar)
        train_loss += loss.item()   # 将当前批次的损失累加到 train_loss 中

        # backward
        optimizer.zero_grad()
        loss.backward()

        # update
        optimizer.step()

        # 每 32 个批次打印当前总损失、重构损失、KL散度损失、当前批次索引
        if idx % 32 == 0:
            print(f"Training loss {loss: .3f} in Step {idx}")

    train_losses.append(train_loss / train_num)   # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化

# 模型推理过程：检测阈值设置
vae.eval()
scores = []

with torch.no_grad():
    for data in train_loader:
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        recon_x, mu, logvar = vae(inputs)

        score = calculate_novelty_score(recon_x, inputs, mu, logvar)
        scores.extend(score)

    # 拟合重构误差的正态分布
    scores = np.array(scores)
    mu, std = norm.fit(scores)
    confidence = 0.9
    threshold = norm.ppf(confidence, loc=mu, scale=std)

    print("====== Unsupervised Threshold Fitting Done ======")
    print(f"μ = {mu:.6f}, σ = {std:.6f}")
    print(f"Threshold (at {confidence * 100:.1f}% confidence): {threshold:.6f}")

    # 保存阈值供后续推理阶段使用
    with open("vae_threshold.json", "w") as f:
        json.dump({
            "mu": float(mu),
            "std": float(std),
            "threshold": float(threshold),
            "confidence": float(confidence)
        }, f)

# 加载训练阶段设定的阈值
with open("vae_threshold.json", "r") as f:
    threshold_info = json.load(f)

threshold = threshold_info["threshold"]

# 模型推理过程：新颖性检测
vae.eval()
valid_loss = 0.0
true_labels = []
scores = []
valid_num = len(val_loader.dataset)    # 获取验证集样本总数

with torch.no_grad():
    for data in val_loader:
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        recon_x, mu, logvar = vae(inputs)

        loss = vae_loss(recon_x, inputs, mu, logvar)
        valid_loss += loss.item()

        score = calculate_novelty_score(recon_x, inputs, mu, logvar)
        scores.extend(score)
        true_labels.extend(labels.cpu().numpy())

    valid_losses.append(valid_loss / valid_num)  # 记录平均验证损失

    scheduler.step(valid_loss)  # 根据验证损失调整学习率

    # 打印当前epoch的验证损失、重构损失、KL散度损失
    print(f"Valid loss {valid_loss / valid_num: .3f} in epoch {epoch}")

    # 使用验证集上的新颖性评分和标签计算AUC
    roc_auc = evaluate_roc_auc(np.array(scores), np.array(true_labels))
    print(f"AUC on validation set: {roc_auc:.4f}")

    # 使用ROC曲线找到最佳阈值
    best_threshold = threshold
    print(f"Best threshold: {threshold}")

    # 根据阈值判断异常点
    predictions = (np.array(scores) > threshold).astype(int)

    # 混淆矩阵
    cm = confusion_matrix(true_labels, predictions)
    print("Confusion Matrix:")
    print(cm)
    print("Classification Report:")
    print(classification_report(true_labels, predictions))

    # 分离正常和异常数据的新颖性得分
    scores_array = np.array(scores)
    true_labels_array = np.array(true_labels)  # 根据真实标签true_labels_array，将新颖性得分scores_array分为两组
    normal_scores = scores_array[true_labels_array == 0]  # 正常数据得分
    anomaly_scores = scores_array[true_labels_array == 1]  # 异常数据得分

    # 绘制重构误差分布柱状图
    plt.figure(figsize=(10, 6))  # 设置图表大小
    plt.hist(normal_scores, bins=20, alpha=0.7, color='blue', edgecolor='black', label='Normal Data')  # 绘制正常数据直方图
    plt.hist(anomaly_scores, bins=20, alpha=0.7, color='red', edgecolor='black', label='Novel Data')  # 绘制新颖数据直方图

    # 添加阈值分割线
    plt.axvline(best_threshold, color='black', linestyle='dashed', linewidth=2,
                label=f'Threshold = {best_threshold:.2f}')  # 添加阈值分割线

    plt.title('Reconstruction Error Distribution with Threshold')
    plt.xlabel('Novelty Score')
    plt.ylabel('Frequency')
    plt.legend()
    plt.show()


