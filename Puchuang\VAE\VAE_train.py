import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import onnx

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('E:\\pythonProject\\Puchuang\\VAE\\detection_normal_1500_15_train_2048.mat')
        normal = train_normal['detection_normal_1500_15_train_2048']

        self.normal_data = normal[0, 0:1843200]   # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 64

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='mean') * x.size(1)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss


# 计算新颖性评分
def calculate_novelty_score(recon_x, x, mu, logvar, recon_weight=0.5, kl_weight=0.5):
    recon_loss = F.mse_loss(recon_x.view_as(x), x, reduction='none').mean(dim=tuple(range(1, x.dim())))
    kl_divergence = -0.5 * torch.mean(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
    novelty_score = recon_weight * recon_loss + kl_weight * kl_divergence
    return novelty_score

# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, latent_dim * 2),   # 同时输出：隐空间的均值向量mu、对数方差向量log_var
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, input_dim),
            nn.Sigmoid(),
        )

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        epsilon = torch.randn_like(mu)                 # 生成与mu形状相同的张量，其元素是从标准正态分布N(0,1)中随机抽取的
        return mu + epsilon * torch.exp(logvar / 2)    # 生成隐变量z，该变量服从以mu为均值，exp(logvar/2)为标准差的正态分布

    def forward(self, x):
        org_size = x.size()     # 获取输入x的原始尺寸
        batch = org_size[0]     # 获取批次大小
        x = x.view(batch, -1)   # 将x展平为二维向量，便于处理

        h = self.encoder(x)                  # 编码器处理输入x
        mu, logvar = h.chunk(2, dim=1)       # 将编码器输出分为两部分：均值mu和对数方差logvar

        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decoder(z).view(org_size)   # 解码器重构输入，并恢复原始尺寸

        return calculate_novelty_score(recon_x, x, mu, logvar).view(-1,1)


# 初始化模型、优化器
vae = VAE(input_dim, inter_dim, latent_dim).to(device)
optimizer = optim.Adam(vae.parameters(), lr=0.002, weight_decay=1e-4)    # 设置L2正则化参数
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, verbose=True)


# 训练 & 验证
epochs = 6
# 初始化绘图数据
train_losses = []

# 创建绘图窗口
plt.figure(figsize=(10, 5))
plt.ion()   # 开启交互模式

best_loss = 1e9
best_epoch = 0

for epoch in range(epochs):
    print(f"Epoch {epoch}")
    # 将模型设置为训练模式
    vae.train()

    train_loss = 0.0
    train_num = len(train_loader.dataset)   # 获取训练集样本总数

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        batch = inputs.size(0)  # 获取当前样本数

        # forward
        loss = vae(inputs)

        train_loss += loss.item()   # 将当前批次的损失累加到 train_loss 中

        # backward
        optimizer.zero_grad()
        loss.backward()

        # update
        optimizer.step()

        # 每 32 个批次打印当前总损失、重构损失、KL散度损失、当前批次索引
        if idx % 32 == 0:
            print(f"Training loss {loss: .3f} in Step {idx}")

    train_losses.append(train_loss / train_num)   # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化


# 保存
save_dir = r"E:\pythonProject\Puchuang\VAE\VAE.pth"  # 保存路径
torch.save(vae.state_dict(), save_dir)


# 保存成ONNX
x = torch.randn(2, 2048, requires_grad=True)
torch.onnx.export(vae,               # model being run
                  x,             # 模型输入
                  "VAE.onnx",   # 模型存储位置
                  export_params=True,        # store the trained parameter weights inside the model file
                  opset_version=10,   #     the ONNX version to export the model to
                  do_constant_folding=True,  # whether to execute constant folding for optimization
                  input_names = ['input'],   # the model's input names
                  output_names = ['output'], # the model's output names
                  # variable length axes
                  dynamic_axes={'input' : {0 : 'batch_size'},
                                'output' : {0 : 'batch_size'}})